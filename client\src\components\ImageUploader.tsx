import { useState, useRef, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { Upload, Link as LinkIcon, Clipboard } from "lucide-react";
import { SampleGallery } from "./SampleGallery";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ImageUploaderProps {
  language: Language;
  onUploadStart: (file: File) => void;
  onUploadComplete: (processId: number) => void;
}

export function ImageUploader({ language, onUploadStart, onUploadComplete }: ImageUploaderProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Debug component mount
  useEffect(() => {
    console.log('ImageUploader component mounted');
    console.log('fileInputRef:', fileInputRef);
    console.log('Initial fileInputRef.current:', fileInputRef.current);
  }, []);

  // Debug fileInputRef changes
  useEffect(() => {
    console.log('fileInputRef.current changed:', fileInputRef.current);
  }, [fileInputRef.current]);

  // Supported image formats
  const SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff',
    'image/tif',
    'image/svg+xml'
  ];

  const SUPPORTED_IMAGE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.bmp',
    '.tiff',
    '.tif',
    '.svg'
  ];

  const handleFileSelect = useCallback(async (file: File) => {
    console.log('File selected:', file.name, file.type, file.size);

    // Check MIME type
    const isSupportedMimeType = SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase());

    // Check file extension as fallback
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isSupportedExtension = SUPPORTED_IMAGE_EXTENSIONS.includes(fileExtension);

    // Also check if mimetype starts with 'image/' for broader compatibility
    const isImageMimeType = file.type.startsWith('image/');

    if (!(isSupportedMimeType || (isImageMimeType && isSupportedExtension))) {
      console.log('File type not supported:', file.type, fileExtension);
      toast({
        title: t("upload.error.invalidType"),
        description: t("upload.error.invalidTypeDesc").replace('{formats}', SUPPORTED_IMAGE_EXTENSIONS.join(', ')),
        variant: "destructive",
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      console.log('File too large:', file.size);
      toast({
        title: t("upload.error.fileTooLarge"),
        description: t("upload.error.fileTooLargeDesc"),
        variant: "destructive",
      });
      return;
    }

    console.log('Starting upload...');
    setIsUploading(true);
    onUploadStart(file);

    try {
      const formData = new FormData();
      formData.append('image', file);

      console.log('Sending API request...');
      const response = await apiRequest('POST', '/api/upload', formData);
      const result = await response.json();

      console.log('Upload successful:', result);
      onUploadComplete(result.id);

      toast({
        title: t("upload.success.title"),
        description: t("upload.success.desc"),
      });
    } catch (error) {
      console.error('Upload failed:', error);
      toast({
        title: t("upload.error.failed"),
        description: t("upload.error.failedDesc"),
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [onUploadStart, onUploadComplete, toast, t]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input change triggered');
    const files = e.target.files;
    console.log('Files selected:', files?.length || 0);

    if (files && files.length > 0) {
      console.log('Processing file:', files[0].name);
      handleFileSelect(files[0]);
    } else {
      console.log('No files selected');
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFileSelect]);

  const handlePasteFromClipboard = useCallback(async () => {
    try {
      const clipboardItems = await navigator.clipboard.read();
      for (const item of clipboardItems) {
        if (item.types.includes('image/png') || item.types.includes('image/jpeg')) {
          const blob = await item.getType(item.types.find(type => type.startsWith('image/'))!);
          const file = new File([blob], 'clipboard-image.png', { type: blob.type });
          handleFileSelect(file);
          return;
        }
      }
      toast({
        title: t("clipboard.error.noImage"),
        description: t("clipboard.error.noImageDesc"),
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: t("clipboard.error.accessFailed"),
        description: t("clipboard.error.accessFailedDesc"),
        variant: "destructive",
      });
    }
  }, [handleFileSelect, toast]);

  return (
    <div className="max-w-2xl mx-auto mb-12 animate-slide-up" style={{ animationDelay: '0.4s' }}>
      <div
        className={`upload-area rounded-2xl p-12 text-center cursor-pointer transition-all duration-300 ${
          isDragOver ? 'dragover' : ''
        } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={(e) => {
          console.log('Upload area clicked');
          console.log('fileInputRef.current:', fileInputRef.current);
          console.log('isUploading:', isUploading);
          if (!isUploading) {
            if (fileInputRef.current) {
              console.log('Calling fileInputRef.current.click()');
              fileInputRef.current.click();
            } else {
              console.error('fileInputRef.current is null!');
            }
          }
        }}
      >
        <div className="mb-6">
          <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <Upload className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-2xl font-semibold mb-2">{t("upload.title")}</h3>
          <p className="text-gray-600">{t("upload.description")}</p>
        </div>
        
        <Button
          className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1 mb-4"
          disabled={isUploading}
          onClick={(e) => {
            e.stopPropagation();
            console.log('Upload button clicked');
            console.log('fileInputRef.current:', fileInputRef.current);
            console.log('isUploading:', isUploading);
            if (!isUploading) {
              if (fileInputRef.current) {
                console.log('Calling fileInputRef.current.click() from button');
                fileInputRef.current.click();
              } else {
                console.error('fileInputRef.current is null from button!');
              }
            }
          }}
        >
          <Upload className="h-4 w-4 mr-2" />
          {isUploading ? t("upload.uploading") : t("upload.button")}
        </Button>
        
        <div className="flex justify-center items-center space-x-4 text-sm text-gray-600">
          <Button
            variant="ghost"
            size="sm"
            className="hover:text-purple-600 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement URL paste functionality
              toast({
                title: t("feature.comingSoon"),
                description: t("feature.urlUploadSoon"),
              });
            }}
          >
            <LinkIcon className="h-4 w-4 mr-1" />
            {t("upload.url")}
          </Button>
          <span>•</span>
          <Button
            variant="ghost"
            size="sm"
            className="hover:text-purple-600 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              handlePasteFromClipboard();
            }}
          >
            <Clipboard className="h-4 w-4 mr-1" />
            {t("upload.clipboard")}
          </Button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.tiff,.tif,.svg,image/*"
          onChange={handleFileInputChange}
          style={{
            position: 'absolute',
            left: '-9999px',
            opacity: 0,
            pointerEvents: 'none'
          }}
        />
      </div>
      
      <SampleGallery language={language} onSampleSelect={handleFileSelect} />
      
      <p className="text-xs text-gray-600 text-center mt-6">
        {t("terms.notice")} <a href="/terms" className="text-purple-600 hover:underline">{t("terms.tos")}</a>.{" "}
        {t("terms.privacy.notice")} <a href="/privacy" className="text-purple-600 hover:underline">{t("terms.privacy")}</a>.
      </p>
    </div>
  );
}
